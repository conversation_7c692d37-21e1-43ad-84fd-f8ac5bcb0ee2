import smtplib
import random
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from email.utils import formataddr
from typing import Optional

from app.settings.config import settings
from app.log import logger


class EmailService:
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_name = settings.SMTP_FROM_NAME
        self.use_tls = settings.SMTP_USE_TLS

    def generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))

    async def send_email(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None) -> bool:
        """发送邮件"""
        try:
            if not self.smtp_user or not self.smtp_password:
                logger.error("邮件配置不完整，请检查SMTP_USER和SMTP_PASSWORD")
                return False

            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            # 使用formataddr确保From头部格式符合RFC标准
            msg['From'] = formataddr((self.from_name, self.smtp_user))
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')

            # 添加文本内容
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)

            # 添加HTML内容
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)

            # 连接SMTP服务器并发送邮件
            server = smtplib.SMTP(self.smtp_host, self.smtp_port)
            if self.use_tls:
                server.starttls()
            server.login(self.smtp_user, self.smtp_password)
            server.send_message(msg)
            server.quit()

            logger.info(f"邮件发送成功: {to_email}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {to_email}, 错误: {str(e)}")
            return False

    def get_verification_email_template(self, code: str, code_type: str) -> tuple[str, str]:
        """获取验证码邮件模板"""
        if code_type == "register":
            subject = "欢迎注册 AI-clear - 邮箱验证码"
            purpose = "注册账户"
        elif code_type == "reset_password":
            subject = "AI-clear - 密码重置验证码"
            purpose = "重置密码"
        else:
            subject = "AI-clear - 邮箱验证码"
            purpose = "验证邮箱"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50;">AI-clear</h1>
                </div>
                
                <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; margin-bottom: 20px;">邮箱验证码</h2>
                    <p>您好！</p>
                    <p>您正在{purpose}，请使用以下验证码完成验证：</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <span style="display: inline-block; padding: 15px 30px; background: #3498db; color: white; font-size: 24px; font-weight: bold; border-radius: 5px; letter-spacing: 5px;">{code}</span>
                    </div>
                    
                    <p style="color: #e74c3c; font-weight: bold;">验证码有效期为10分钟，请及时使用。</p>
                    <p>如果这不是您的操作，请忽略此邮件。</p>
                </div>
                
                <div style="text-align: center; color: #7f8c8d; font-size: 12px;">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; 2024 AI-clear. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        AI-clear - 邮箱验证码
        
        您好！
        
        您正在{purpose}，请使用以下验证码完成验证：
        
        验证码：{code}
        
        验证码有效期为10分钟，请及时使用。
        如果这不是您的操作，请忽略此邮件。
        
        此邮件由系统自动发送，请勿回复。
        © 2024 AI-clear. All rights reserved.
        """

        return subject, html_content, text_content

    async def send_verification_code(self, to_email: str, code: str, code_type: str) -> bool:
        """发送验证码邮件"""
        subject, html_content, text_content = self.get_verification_email_template(code, code_type)
        return await self.send_email(to_email, subject, html_content, text_content)


# 创建全局邮件服务实例
email_service = EmailService()
