from fastapi import APIRouter, Query

from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.controllers.invite import invite_controller
from app.schemas import Success, SuccessExtra

router = APIRouter()


@router.get("/my_invite_data", summary="获取我的推广数据", dependencies=[DependAuth])
async def get_my_invite_data():
    """获取当前用户的推广数据，包括邀请码、邀请链接、统计信息和邀请记录"""
    user_id = CTX_USER_ID.get()
    invite_data = await invite_controller.get_user_invite_data(user_id)
    return Success(data=invite_data.model_dump())


@router.get("/my_invite_records", summary="获取我的邀请记录", dependencies=[DependAuth])
async def get_my_invite_records(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100)
):
    """分页获取当前用户的邀请记录"""
    user_id = CTX_USER_ID.get()
    records, total = await invite_controller.get_invite_records_by_user(user_id, page, page_size)
    
    # 转换为字典格式
    data = [record.model_dump() for record in records]
    
    return SuccessExtra(
        data=data,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/my_invite_statistics", summary="获取我的邀请统计", dependencies=[DependAuth])
async def get_my_invite_statistics():
    """获取当前用户的邀请统计数据"""
    user_id = CTX_USER_ID.get()
    statistics = await invite_controller.get_invite_statistics(user_id)
    return Success(data=statistics)


@router.get("/validate_invite_code", summary="验证邀请码")
async def validate_invite_code(
    invite_code: str = Query(..., description="邀请码")
):
    """验证邀请码是否有效"""
    user = await invite_controller.validate_invite_code(invite_code)
    
    if user:
        return Success(data={
            "valid": True,
            "inviter_username": user.username,
            "inviter_id": user.id
        })
    else:
        return Success(data={
            "valid": False,
            "inviter_username": None,
            "inviter_id": None
        })
