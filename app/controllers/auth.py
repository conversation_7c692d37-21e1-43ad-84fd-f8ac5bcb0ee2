from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.models.admin import User, InviteRecord, UserCredit, Role
from app.schemas.auth import UserRegisterRequest, ResetPasswordRequest
from app.utils.password import get_password_hash, verify_password
from app.utils.verification import verification_service
from app.controllers.user_credit import user_credit_controller
from app.log import logger


class AuthController:
    def __init__(self):
        pass

    async def send_verification_code(self, email: str, code_type: str) -> Tuple[bool, str]:
        """发送验证码"""
        # 检查邮箱是否已注册（仅在注册时检查）
        if code_type == "register":
            existing_user = await User.filter(email=email).first()
            if existing_user:
                return False, "该邮箱已注册，请直接登录"
        elif code_type == "reset_password":
            existing_user = await User.filter(email=email).first()
            if not existing_user:
                return False, "该邮箱未注册，请先注册账户"

        # 发送验证码
        return await verification_service.send_verification_code(email, code_type)

    async def verify_code(self, email: str, code: str, code_type: str) -> Tuple[bool, str]:
        """验证验证码"""
        return await verification_service.verify_code(email, code, code_type, mark_used=False)

    @atomic()
    async def register_user(self, register_data: UserRegisterRequest) -> Tuple[bool, str, Optional[User]]:
        """用户注册"""
        try:
            # 验证邮箱验证码
            code_valid, code_msg = await verification_service.verify_code(
                register_data.email, 
                register_data.verification_code, 
                "register", 
                mark_used=True
            )
            if not code_valid:
                return False, code_msg, None

            # 检查邮箱是否已注册
            existing_user = await User.filter(email=register_data.email).first()
            if existing_user:
                return False, "该邮箱已注册", None

            # 检查用户名是否已存在
            existing_username = await User.filter(username=register_data.username).first()
            if existing_username:
                return False, "用户名已存在", None

            # 处理邀请码
            inviter_id = 0
            if register_data.invite_code:
                inviter = await User.filter(invite_code=register_data.invite_code).first()
                if inviter:
                    inviter_id = inviter.id
                else:
                    logger.warning(f"无效的邀请码: {register_data.invite_code}")

            # 生成用户的邀请码
            user_invite_code = await verification_service.get_unique_invite_code()

            # 创建用户
            user = await User.create(
                email=register_data.email,
                username=register_data.username,
                password=get_password_hash(register_data.password),
                affman=inviter_id,
                invite_code=user_invite_code,
                is_active=True
            )

            # 为新用户分配"普通用户"角色
            user_role = await Role.filter(name="普通用户").first()
            if user_role:
                await user.roles.add(user_role)
                logger.info(f"为用户分配普通用户角色: {register_data.username}")
            else:
                logger.warning("未找到'普通用户'角色，请检查系统初始化")

            # 创建用户积分记录（新用户默认1000积分）
            await user_credit_controller.get_or_create_user_credit(user.id)

            # 如果有邀请人，创建邀请记录并奖励积分
            if inviter_id > 0:
                # 创建邀请记录
                invite_record = await InviteRecord.create(
                    inviter_id=inviter_id,
                    invitee_id=user.id,
                    invite_code=register_data.invite_code,
                    reward_credits=1000,
                    is_rewarded=False
                )

                # 给邀请人奖励积分
                try:
                    await user_credit_controller.recharge_credits(
                        user_id=inviter_id,
                        amount=1000,
                        description=f"邀请用户注册奖励，被邀请人: {register_data.username}"
                    )
                    reward_success = True
                except Exception as e:
                    logger.error(f"邀请奖励发放失败: {str(e)}")
                    reward_success = False

                if reward_success:
                    invite_record.is_rewarded = True
                    await invite_record.save()
                    logger.info(f"邀请奖励发放成功: 邀请人ID {inviter_id}, 被邀请人: {register_data.username}")

            logger.info(f"用户注册成功: {register_data.email}, 用户名: {register_data.username}")
            return True, "注册成功", user

        except Exception as e:
            logger.error(f"用户注册异常: {register_data.email}, 错误: {str(e)}")
            return False, "注册失败，请稍后重试", None

    @atomic()
    async def reset_password(self, reset_data: ResetPasswordRequest) -> Tuple[bool, str]:
        """重置密码"""
        try:
            # 验证邮箱验证码
            code_valid, code_msg = await verification_service.verify_code(
                reset_data.email, 
                reset_data.verification_code, 
                "reset_password", 
                mark_used=True
            )
            if not code_valid:
                return False, code_msg

            # 查找用户
            user = await User.filter(email=reset_data.email).first()
            if not user:
                return False, "用户不存在"

            # 更新密码
            user.password = get_password_hash(reset_data.new_password)
            await user.save()

            logger.info(f"密码重置成功: {reset_data.email}")
            return True, "密码重置成功"

        except Exception as e:
            logger.error(f"密码重置异常: {reset_data.email}, 错误: {str(e)}")
            return False, "密码重置失败，请稍后重试"

    async def check_email_exists(self, email: str) -> bool:
        """检查邮箱是否已注册"""
        user = await User.filter(email=email).first()
        return user is not None

    async def check_username_exists(self, username: str) -> bool:
        """检查用户名是否已存在"""
        user = await User.filter(username=username).first()
        return user is not None


# 创建全局认证控制器实例
auth_controller = AuthController()
